<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.DataProcessRecordMapper">
    <select id="selectNodeProcessCountList" resultType="com.trs.ai.moye.monitor.entity.NodeProcessCountInfo">
        SELECT
        data_model_id as dataModelId,
        pod_ip as node,
        COUNT(*) AS processCount,
        sumIf(1, is_error = 1) AS processErrorCount,
        sum(processing_time) AS processingTimeMillis
        FROM data_process_record
        WHERE storage_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        GROUP BY data_model_id, pod_ip
    </select>

    <select id="selectLastDayCountByModelId" resultType="com.trs.ai.moye.homepage.entity.HomePageDwdStatistics">
        select data_model_id,
        data_source_name as data_model_name,
        toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
        'STREAM' as type,
        SUM(CASE WHEN is_error = 1 THEN 1 ELSE 0 END) AS fail_count,
        SUM(CASE WHEN is_error = 0 THEN 1 ELSE 0 END) AS success_count,
        COUNT(*) AS total_count,
        sum(executed_operator_count) as operator_count,
        toTimezone(now(), 'Asia/Shanghai') as syncTime
        from data_process_record
        <where>
            end_time is not null
            <if test="startTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
            </if>
            <if test="endTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
            </if>
            <if test="dataModelIds !=null">
                and data_model_id in
                <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                    #{dataModelId}
                </foreach>
            </if>
        </where>
        GROUP BY data_model_id,
        data_source_name,
        time,
        type;
    </select>

    <select id="selectLastDayCountByModelIdTheme" resultType="com.trs.ai.moye.homepage.entity.HomePageThemeStatistics">
        select data_model_id,
        data_source_name as data_model_name,
        toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
        'STREAM' as type,
        SUM(CASE WHEN is_error = 1 THEN 1 ELSE 0 END) AS fail_count,
        SUM(CASE WHEN is_error = 0 THEN 1 ELSE 0 END) AS success_count,
        COUNT(*) AS total_count,
        sum(executed_operator_count) as operator_count,
        toTimezone(now(), 'Asia/Shanghai') as syncTime
        from data_process_record
        <where>
            end_time is not null
            <if test="startTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
            </if>
            <if test="endTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
            </if>
            <if test="dataModelIds !=null">
                and data_model_id in
                <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                    #{dataModelId}
                </foreach>
            </if>
        </where>
        GROUP BY data_model_id,
        data_source_name,
        time,
        type;
    </select>

    <select id="selectLastDayCountByModelIdSubject"
        resultType="com.trs.ai.moye.homepage.entity.HomePageSubjectStatistics">
        select data_model_id,
        data_source_name as data_model_name,
        toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
        'STREAM' as type,
        SUM(CASE WHEN is_error = 1 THEN 1 ELSE 0 END) AS fail_count,
        SUM(CASE WHEN is_error = 0 THEN 1 ELSE 0 END) AS success_count,
        COUNT(*) AS total_count,
        sum(executed_operator_count) as operator_count,
        toTimezone(now(), 'Asia/Shanghai') as syncTime
        from data_process_record
        <where>
            end_time is not null
            <if test="startTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
            </if>
            <if test="endTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
            </if>
            <if test="dataModelIds !=null">
                and data_model_id in
                <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                    #{dataModelId}
                </foreach>
            </if>
        </where>
        GROUP BY data_model_id,
        data_source_name,
        time,
        type;
    </select>
</mapper>