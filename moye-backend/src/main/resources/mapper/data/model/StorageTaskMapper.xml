<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.StorageTaskMapper">
    <resultMap id="StorageTaskMap" type="com.trs.ai.moye.data.model.entity.StorageTask">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="data_model_id" jdbcType="INTEGER" property="dataModelId"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="access_mode" jdbcType="VARCHAR" property="accessMode"/>
        <result column="read_success_count" jdbcType="INTEGER" property="readSuccessCount"/>
        <result column="read_fail_count" jdbcType="INTEGER" property="readFailCount"/>
        <result column="write_count_info" jdbcType="VARCHAR" property="writeCountInfo"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="allow_sync" jdbcType="BOOLEAN" property="allowSync"/>
        <result column="last_sync_time" jdbcType="TIMESTAMP" property="lastSyncTime"/>
        <result column="storage_job_id" jdbcType="VARCHAR" property="storageJobId"/>
        <result column="execution_status" jdbcType="VARCHAR" property="executionStatus"/>
        <result column="error_message" jdbcType="VARCHAR" property="errorMessage"/>
        <result column="error_msg_read_flag" jdbcType="INTEGER" property="errorMsgReadFlag"/>
        <result column="storage_ids" jdbcType="VARCHAR" property="storageIds"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
    </resultMap>
    <update id="updateReadErrorMessages">
        update storage_task
        set error_msg_read_flag = 1
        where id = #{id}
    </update>

    <select id="getPage" resultMap="StorageTaskMap">
        select * from storage_task
        <where>
            data_model_id = #{dataModelId}
            <if test="null != beginTime and null != endTime">
                AND start_time BETWEEN #{beginTime} AND #{endTime}
            </if>
            <if test="null != executionStatus">
                AND execution_status = #{executionStatus}
            </if>
            <if test="null != search and search.fields.contains('batchNo')">
                AND batch_no like '%${search.keyword}%'
            </if>
            <if test="null != showOnlyHasData and showOnlyHasData">
                AND (
                 arrayExists(
                x -> (toInt64(JSONExtractRaw(x, 'storageId')) in
                <foreach collection="storageIds" item="storageId" open="(" separator="," close=")">
                    #{storageId}
                </foreach>
                    and (toInt64(JSONExtractRaw(x, 'successCount')) > 0 OR
                    toInt64(JSONExtractRaw(x, 'failCount')) > 0)
                ),
                JSONExtractArrayRaw(write_count_info)
                ))
            </if>
            <if test="null!=showOnlyHasData and !showOnlyHasData">
                AND ( arrayExists(
                x -> (toInt64(JSONExtractRaw(x, 'storageId')) in
                <foreach collection="storageIds" item="storageId" open="(" separator="," close=")">
                    #{storageId}
                </foreach>
                ),
                JSONExtractArrayRaw(write_count_info)
                ))

            </if>
        </where>
        ORDER BY start_time DESC
    </select>

    <select id="countErrorByDataModelId" resultType="java.lang.Integer">
        select count(id)
        from storage_task
        WHERE data_model_id = #{id}
          AND (execution_status = 'FAILED' OR execution_status = 'EXECUTION_FAILED')
          AND error_msg_read_flag = 0
    </select>
    <select id="countAllUnreadErrors" resultType="com.trs.ai.moye.common.dao.GroupCount">
        select `data_model_id` as value,
        count(id) as count
        from storage_task
        WHERE (execution_status = 'FAILED'
           OR execution_status = 'EXECUTION_FAILED')
          AND error_msg_read_flag = 0
        GROUP BY data_model_id
    </select>

    <select id="selectByBatchNo" resultMap="StorageTaskMap">
        select *
        from storage_task
        where batch_no = #{batchNo}
    </select>

    <select id="selectLastDayCountByModelId" resultType="com.trs.ai.moye.homepage.entity.HomePageDwdStatistics">
        <!--        select data_model_id, null as data_model_name,-->
        <!--        'STORAGE' as type,toDate(end_time) as time ,-->
        <!--        0 as operatorCount,-->
        <!--        sum(arraySum(-->
        <!--        arrayMap(-->
        <!--        x -> JSONExtractInt(x, 'successCount'),-->
        <!--        JSONExtractArrayRaw(write_count_info)-->
        <!--        )-->
        <!--        )) AS success_count,-->
        <!--        sum(arraySum(-->
        <!--        arrayMap(-->
        <!--        x -> JSONExtractInt(x, 'failCount'),-->
        <!--        JSONExtractArrayRaw(write_count_info)-->
        <!--        )-->
        <!--        )) AS fail_count,-->
        <!--        success_count + fail_count as total_count,-->
        <!--        now() as syncTime-->
        <!--        from storage_task-->
        <!--        <where>-->
        <!--            layer_id = #{layerId} and end_time is not null-->
        <!--            <if test="startTime!=null">-->
        <!--                and toDateTime(end_time) &gt;= toDateTime(#{startTime})-->
        <!--            </if>-->
        <!--            <if test="endTime!=null">-->
        <!--                and toDateTime(end_time) &lt;= toDateTime(#{endTime})-->
        <!--            </if>-->
        <!--        </where>-->
        <!--        group by data_model_id,time-->
        SELECT
        data_model_id,
        null as data_model_name,
        'STORAGE' as type,
        toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
        JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'storageId') AS storage_id,
        sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'successCount')) AS success_count,
        sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'failCount')) AS fail_count,
        success_count + fail_count as total_count,
        toTimezone(now(), 'Asia/Shanghai') as syncTime,
        null as storageName
        FROM
        storage_task
        ARRAY JOIN
        arrayEnumerate(JSONExtractArrayRaw(write_count_info)) AS index
        <where>
            layer_id = #{layerId} and end_time is not null
            <if test="startTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
            </if>
            <if test="endTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
            </if>
        </where>
        GROUP BY
        data_model_id,
        time,
        storage_id
    </select>

    <select id="countStorageTotalByDataModelIdAndTimeRange" resultType="java.lang.Long">
        select sum(arraySum(
        arrayMap(
        x -> JSONExtractInt(x, 'successCount'),
        JSONExtractArrayRaw(write_count_info)
        )
        )) as total_count
        from storage_task
        <where>
            <if test="dataModelId != null">
                data_model_id = #{dataModelId}
            </if>
            <if test="startTime != null">
                and start_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and end_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <select id="selectOdsStatistics" resultType="com.trs.ai.moye.homepage.entity.HomePageOdsStorageStatistics">
        <!--        select data_model_id, null as data_model_name,toDate(end_time) as time ,-->
        <!--        sum(arraySum(-->
        <!--        arrayMap(-->
        <!--        x -> JSONExtractInt(x, 'successCount'),-->
        <!--        JSONExtractArrayRaw(write_count_info)-->
        <!--        )-->
        <!--        )) AS success_count,-->
        <!--        sum(arraySum(-->
        <!--        arrayMap(-->
        <!--        x -> JSONExtractInt(x, 'failCount'),-->
        <!--        JSONExtractArrayRaw(write_count_info)-->
        <!--        )-->
        <!--        )) AS fail_count,-->
        <!--        success_count + fail_count as total_count,-->
        <!--        now() as syncTime,-->
        <!--        null as storageName-->
        <!--        from storage_task-->
        <!--        <where>-->
        <!--            layer_id = 1 and end_time is not null-->
        <!--            <if test="startTime!=null">-->
        <!--                and toDateTime(end_time) &gt;= toDateTime(#{startTime})-->
        <!--            </if>-->
        <!--            <if test="endTime!=null">-->
        <!--                and toDateTime(end_time) &lt;= toDateTime(#{endTime})-->
        <!--            </if>-->
        <!--        </where>-->
        <!--        group by data_model_id,time-->
        SELECT
        data_model_id,
        null as data_model_name,
        toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
        JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'storageId') AS storage_id,
        sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'successCount')) AS success_count,
        sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'failCount')) AS fail_count,
        success_count + fail_count as total_count,
        now() as syncTime,
        null as storageName
        FROM
        storage_task
        ARRAY JOIN
        arrayEnumerate(JSONExtractArrayRaw(write_count_info)) AS index
        WHERE
        layer_id = 1
        AND end_time IS NOT NULL
        <if test="startTime!=null">
            and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
        </if>
        <if test="endTime!=null">
            and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
        </if>
        GROUP BY
        data_model_id,
        time,
        storage_id
    </select>

    <select id="selectOdsDispatchStatistics" resultType="com.trs.ai.moye.homepage.entity.HomePageOdsScheduleStatistics">
        select
        SUM(CASE WHEN execution_status='FAILED' THEN 1 ELSE 0 END) as fail_count,
        SUM(CASE WHEN execution_status !='FAILED' THEN 1 ELSE 0 END) as success_count,
        success_count + fail_count as total_count,
        data_model_id ,
        null as data_model_name,
        toTimezone(now(),'Asia/Shanghai') as syncTime,
        toDate(toTimezone(end_time,'Asia/Shanghai')) as time
        from storage_task st
        <where>
            layer_id = 1 and end_time is not null
            <if test="startTime!=null">
                and toDateTime(toTimezone(end_time,'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
            </if>
            <if test="endTime!=null">
                and toDateTime(toTimezone(end_time,'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
            </if>
        </where>
        group by data_model_id,time
    </select>

    <select id="selectStorageIdOneByModelId" resultType="java.lang.Integer">
        SELECT
            JSONExtractInt(JSONExtractArrayRaw(write_count_info)[1], 'storageId') AS first_storage_id
        FROM storage_task
        WHERE data_model_id = #{dataModelId} and write_count_info !='[]' limit 1
    </select>

    <select id="selectLastDayCountByModelIdTheme" resultType="com.trs.ai.moye.homepage.entity.HomePageThemeStatistics">
        SELECT
        data_model_id,
        null as data_model_name,
        'STORAGE' as type,
        toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
        JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'storageId') AS storage_id,
        sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'successCount')) AS success_count,
        sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'failCount')) AS fail_count,
        success_count + fail_count as total_count,
        toTimezone(now(), 'Asia/Shanghai') as syncTime,
        null as storageName
        FROM
        storage_task
        ARRAY JOIN
        arrayEnumerate(JSONExtractArrayRaw(write_count_info)) AS index
        <where>
            layer_id = #{layerId} and end_time is not null
            <if test="startTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
            </if>
            <if test="endTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
            </if>
        </where>
        GROUP BY
        data_model_id,
        time,
        storage_id
    </select>

    <select id="selectLastDayCountByModelIdSubject"
        resultType="com.trs.ai.moye.homepage.entity.HomePageSubjectStatistics">
        SELECT
        data_model_id,
        null as data_model_name,
        'STORAGE' as type,
        toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
        JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'storageId') AS storage_id,
        sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'successCount')) AS success_count,
        sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'failCount')) AS fail_count,
        success_count + fail_count as total_count,
        toTimezone(now(), 'Asia/Shanghai') as syncTime,
        null as storageName
        FROM
        storage_task
        ARRAY JOIN
        arrayEnumerate(JSONExtractArrayRaw(write_count_info)) AS index
        <where>
            layer_id = #{layerId} and end_time is not null
            <if test="startTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
            </if>
            <if test="endTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
            </if>
        </where>
        GROUP BY
        data_model_id,
        time,
        storage_id
    </select>
</mapper>