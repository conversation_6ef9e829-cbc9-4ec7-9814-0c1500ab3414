<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.model.dao.BatchTaskRecordMapper">
  <!-- 结果集映射 -->
  <resultMap id="BaseResultMap" type="com.trs.ai.moye.data.model.entity.BatchTaskRecord">
    <result column="execute_id" jdbcType="VARCHAR" property="executeId" />
    <result column="application_id" jdbcType="VARCHAR" property="applicationId" />
    <result column="trigger_mode" property="triggerMode" />
    <result column="task_id" jdbcType="INTEGER" property="taskId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="is_error" jdbcType="TINYINT" property="isError" />
    <result column="error_msg_read_flag" jdbcType="INTEGER" property="errorMsgReadFlag" />
    <result column="storage_success_count" jdbcType="BIGINT" property="storageSuccessCount" />
    <result column="layer" jdbcType="VARCHAR" property="layer"/>
    <result column="write_count_info" jdbcType="VARCHAR" property="writeCountInfo"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
    <result column="status" jdbcType="VARCHAR" property="status"/>
    <result column="retry_xxl_job_id" jdbcType="INTEGER" property="retryXxlJobId"/>
  </resultMap>
  <!-- 属性列表 -->
  <sql id="Base_Column_List">
    <trim suffixOverrides=",">
      execute_id,
      application_id,
      trigger_mode,
      task_id,
      task_name,
      start_time,
      end_time,
      is_error,
      error_msg_read_flag,
      storage_success_count,
      layer,
      write_count_info,
      `status`,
      retry_xxl_job_id,
    </trim>
  </sql>
    <update id="updateReadErrorMessages">
        update batch_task_record
        set error_msg_read_flag = 1
        where execute_id = #{id}
    </update>

    <select id="selectBatchTaskMonitorList" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List"/>
      FROM batch_task_record
      <where>
          AND task_id = #{modelId,jdbcType=INTEGER}
          <if test="timeRangeParams != null and null != timeRangeParams.minTime">
              AND start_time &gt;= #{timeRangeParams.minTime}
          </if>
          <if test="timeRangeParams != null and null != timeRangeParams.maxTime">
              <![CDATA[ AND start_time <= #{timeRangeParams.maxTime} ]]>
          </if>

          <if test="taskStatus != null ">
              AND COALESCE(`status`,
                  CASE
                      WHEN is_error = 1 THEN 'FAILED'
                      WHEN end_time IS NULL THEN 'RUNNING'
                      ELSE 'SUCCESS'
                  END
              ) = #{taskStatus}
          </if>

          <if test="null != searchParams">
              <foreach collection="searchParams.fields" item="key" separator=" AND ">
                  <choose>
                      <when test="key == 'executeId'">
                          AND execute_id like '%${searchParams.keyword}%'
                      </when>
                      <when test="key == 'taskName'">
                          AND task_name like '%${searchParams.keyword}%'
                      </when>
                  </choose>
              </foreach>
          </if>
      </where>
      ORDER BY start_time DESC
  </select>
    <select id="countErrorByDataModelId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM batch_task_record
        WHERE task_id = #{id,jdbcType=INTEGER}
        AND is_error = 1
        AND error_msg_read_flag = 0
    </select>
    <select id="countAllUnreadErrors" resultType="com.trs.ai.moye.common.dao.GroupCount">
        SELECT COUNT(1) as count,
               task_id as value
        FROM batch_task_record
        WHERE is_error = 1
          AND error_msg_read_flag = 0
        GROUP BY task_id
    </select>

  <select id="selectLastDayCountByModelId" resultType="com.trs.ai.moye.homepage.entity.HomePageDwdStatistics">
      select task_id as data_model_id,
             null AS data_model_name,
            toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
             'BATCH' as type,
             0 as operatorCount,
             SUM(CASE WHEN is_error = 1 THEN 1 ELSE 0 END) AS fail_count,
             SUM(CASE WHEN is_error = 0 THEN 1 ELSE 0 END) AS success_count,
             COUNT(*) AS total_count,
            toTimezone(now(),'Asia/Shanghai') as sync_time
      from batch_task_record where layer ='DWD' and end_time is not null
      <if test="startTime!=null">
          and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
      </if>
      <if test="endTime!=null">
          and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
      </if>
      GROUP BY
          task_id,
          time,
          type;
    </select>

  <select id="selectBatchStorage" resultType="com.trs.ai.moye.homepage.entity.HomePageDwdStatistics">
      select task_id as data_model_id,
      null AS data_model_name,
      toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
      'STORAGE' as type,
      0 as operatorCount,
      JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'storageId') AS storage_id,
      sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'successCount')) AS success_count,
      sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'failCount')) AS fail_count,
      success_count + fail_count as total_count,
      toTimezone(now() , 'Asia/Shanghai')as sync_time
      from batch_task_record
      ARRAY JOIN
      arrayEnumerate(JSONExtractArrayRaw(write_count_info)) AS index
      where layer = 'DWD' and end_time is not null
      <if test="startTime!=null">
          and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
      </if>
      <if test="endTime!=null">
          and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
      </if>
      GROUP BY
      storage_id,
      task_id,
      time,
      type;
    </select>

  <select id="getThemeStorageCount" resultType="java.lang.Long">
      select
      sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'successCount')) AS success_count
      from batch_task_record
      ARRAY JOIN
      arrayEnumerate(JSONExtractArrayRaw(write_count_info)) AS index
      where layer ='THEME' and end_time is not null
      and task_id = #{dataModelId}
      <if test="storageId != null">
          and JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'storageId') = #{storageId}
      </if>
      <if test="startTime!=null">
          and toDateTime(end_time) &gt;= toDateTime(#{startTime})
      </if>
      <if test="endTime!=null">
          and toDateTime(end_time) &lt;= toDateTime(#{endTime})
      </if>
  </select>

  <select id="getThemeProcessCount" resultType="java.lang.Long">
      select SUM(CASE WHEN is_error = 0 THEN 1 ELSE 0 END) AS success_count
      from batch_task_record
      where layer ='THEME' and end_time is not null
      and task_id = #{dataModelId}
      <if test="startTime!=null">
          and toDateTime(end_time) &gt;= toDateTime(#{startTime})
      </if>
      <if test="endTime!=null">
          and toDateTime(end_time) &lt;= toDateTime(#{endTime})
      </if>
    </select>

  <select id="selectAll" resultMap="BaseResultMap">
      select *  from batch_task_record
    </select>

  <update id="updateWriteCountInfo">
          UPDATE batch_task_record
          SET
          write_count_info = #{data.writeCountInfo,jdbcType=VARCHAR,typeHandler=com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler}
          WHERE execute_id = #{data.executeId}
    </update>

  <select id="selectLastDayCountByModelIdTheme" resultType="com.trs.ai.moye.homepage.entity.HomePageThemeStatistics">
      select task_id as data_model_id,
      null AS data_model_name,
      toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
      'BATCH' as type,
      0 as operatorCount,
      SUM(CASE WHEN is_error = 1 THEN 1 ELSE 0 END) AS fail_count,
      SUM(CASE WHEN is_error = 0 THEN 1 ELSE 0 END) AS success_count,
      COUNT(*) AS total_count,
      toTimezone(now(),'Asia/Shanghai') as sync_time
      from batch_task_record where layer ='THEME' and end_time is not null
      <if test="startTime!=null">
          and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
      </if>
      <if test="endTime!=null">
          and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
      </if>
      GROUP BY
      task_id,
      time,
      type;
    </select>

  <select id="selectBatchStorageTheme" resultType="com.trs.ai.moye.homepage.entity.HomePageThemeStatistics">
      select task_id as data_model_id,
      null AS data_model_name,
      toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
      'STORAGE' as type,
      0 as operatorCount,
      JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'storageId') AS storage_id,
      sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'successCount')) AS success_count,
      sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'failCount')) AS fail_count,
      success_count + fail_count as total_count,
      toTimezone(now() , 'Asia/Shanghai')as sync_time
      from batch_task_record
      ARRAY JOIN
      arrayEnumerate(JSONExtractArrayRaw(write_count_info)) AS index
      where layer ='THEME' and end_time is not null
      <if test="startTime!=null">
          and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
      </if>
      <if test="endTime!=null">
          and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
      </if>
      GROUP BY
      storage_id,
      task_id,
      time,
      type;
    </select>

  <select id="selectLastDayCountByModelIdSubject"
        resultType="com.trs.ai.moye.homepage.entity.HomePageSubjectStatistics">
      select task_id as data_model_id,
      null AS data_model_name,
      toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
      'BATCH' as type,
      0 as operatorCount,
      SUM(CASE WHEN is_error = 1 THEN 1 ELSE 0 END) AS fail_count,
      SUM(CASE WHEN is_error = 0 THEN 1 ELSE 0 END) AS success_count,
      COUNT(*) AS total_count,
      toTimezone(now(),'Asia/Shanghai') as sync_time
      from batch_task_record where layer ='SUBJECT' and end_time is not null
      <if test="startTime!=null">
          and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
      </if>
      <if test="endTime!=null">
          and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
      </if>
      GROUP BY
      task_id,
      time,
      type;
    </select>

  <select id="selectBatchStorageSubject" resultType="com.trs.ai.moye.homepage.entity.HomePageSubjectStatistics">
      select task_id as data_model_id,
      null AS data_model_name,
      toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
      'STORAGE' as type,
      0 as operatorCount,
      JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'storageId') AS storage_id,
      sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'successCount')) AS success_count,
      sum(JSONExtractInt(JSONExtractArrayRaw(write_count_info)[index], 'failCount')) AS fail_count,
      success_count + fail_count as total_count,
      toTimezone(now() , 'Asia/Shanghai')as sync_time
      from batch_task_record
      ARRAY JOIN
      arrayEnumerate(JSONExtractArrayRaw(write_count_info)) AS index
      where layer ='SUBJECT' and end_time is not null
      <if test="startTime!=null">
          and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
      </if>
      <if test="endTime!=null">
          and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
      </if>
      GROUP BY
      storage_id,
      task_id,
      time,
      type;
    </select>
</mapper>