package com.trs.ai.moye.data.model.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.data.model.dao.BatchArrangementMapper;
import com.trs.ai.moye.data.model.dao.batch.BatchOperatorMapper;
import com.trs.ai.moye.data.model.dto.arrangement.Canvas;
import com.trs.ai.moye.data.model.dto.arrangement.batch.BatchOperatorDTO;
import com.trs.ai.moye.data.model.dto.arrangement.batch.BatchOperatorPipelineDTO;
import com.trs.ai.moye.data.model.entity.BatchArrangement;
import com.trs.moye.ability.entity.operator.BatchOperator;
import com.trs.moye.ability.enums.ArrangeNodeType;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BatchOperatorPipelineServiceImplTest {

    @Mock
    private BatchArrangementMapper batchArrangementMapper;
    @Mock
    private DataSourceConfigMapper dataSourceConfigMapper;
    @Mock
    private DataStorageMapper dataStorageMapper;
    @Mock
    private BatchOperatorMapper batchOperatorMapper;
    @Mock
    private DataModelMapper dataModelMapper;
    @Mock
    private AbilityMapper abilityMapper;

    @InjectMocks
    private BatchOperatorPipelineServiceImpl batchOperatorPipelineService;

    @BeforeEach
    void setUp() {
        // Initialize mocks
    }

    @Test
    void saveBatchOperatorPipeline() {
        Integer dataModelId = 1;
        BatchOperatorPipelineDTO batchOperatorPipelineDTO = new BatchOperatorPipelineDTO();
        batchOperatorPipelineDTO.setCanvas(new Canvas());
        batchOperatorPipelineDTO.setOperators(List.of(
            BatchOperatorDTO.builder().name("Operator 1").type(ArrangeNodeType.OPERATOR).build(),
            BatchOperatorDTO.builder().name("Operator 2").type(ArrangeNodeType.OPERATOR).build()
        ));

        BatchArrangement batchArrangement = new BatchArrangement();
        batchArrangement.setId(1);
        batchArrangement.setDataModelId(dataModelId);
        when(batchArrangementMapper.selectByDataModelId(dataModelId)).thenReturn(batchArrangement);

        batchOperatorPipelineService.saveBatchOperatorPipeline(dataModelId, batchOperatorPipelineDTO);

        verify(batchArrangementMapper, times(1)).updateById(batchArrangement);
        verify(batchOperatorMapper, times(1)).deleteByArrangementId(1);
        verify(batchOperatorMapper, times(1)).insert(anyList());
        verify(dataModelMapper, times(1)).updateIsArranged(dataModelId, true);
    }

    @Test
    void getBatchOperatorPipeline() {
        Integer dataModelId = 1;
        BatchArrangement batchArrangement = new BatchArrangement();
        batchArrangement.setId(1);
        batchArrangement.setCanvas(new Canvas());
        when(batchArrangementMapper.selectByDataModelId(dataModelId)).thenReturn(batchArrangement);

        List<BatchOperator> operators = List.of(
            BatchOperator.builder().arrangementId(1).displayId(1L).name("Operator 1").abilityId(1).enabled(true).build(),
            BatchOperator.builder().arrangementId(1).displayId(2L).name("Operator 2").abilityId(1).enabled(true).build()
        );
        when(batchOperatorMapper.selectByArrangementId(1)).thenReturn(operators);

        BatchOperatorPipelineDTO result = batchOperatorPipelineService.getBatchOperatorPipeline(dataModelId);

        assertNotNull(result);
        assertEquals(2, result.getOperators().size());
        assertEquals(batchArrangement.getCanvas(), result.getCanvas());
    }
}