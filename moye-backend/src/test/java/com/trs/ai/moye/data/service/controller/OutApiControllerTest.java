package com.trs.ai.moye.data.service.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.ai.moye.out.request.PasswordRequest;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.ai.moye.common.web.response.CustomerResponseBodyConvert;
import com.trs.ai.moye.common.web.response.GlobalExceptionProcessor;
import com.trs.ai.moye.data.service.dto.TableField;
import com.trs.ai.moye.data.service.dto.TableStructDto;
import com.trs.ai.moye.data.service.request.SqlRequest;
import com.trs.ai.moye.data.service.response.DbInfoResponse;
import com.trs.ai.moye.data.service.response.NativeStatementExecuteResult;
import com.trs.ai.moye.data.service.response.StorageInfoListResponse;
import com.trs.ai.moye.out.controller.OutApiController;
import com.trs.ai.moye.out.service.OutApiService;
import com.trs.moye.base.common.utils.AesUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.bind.MethodArgumentNotValidException;

@ExtendWith(MockitoExtension.class)
class OutApiControllerTest {

    @InjectMocks
    private OutApiController outApiController;

    private MockMvc mockMvc;

    @Mock
    private OutApiService outService;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(outApiController)
            .setControllerAdvice(new CustomerResponseBodyConvert(), new GlobalExceptionProcessor())
            .setMessageConverters(new MappingJackson2HttpMessageConverter(JsonUtils.OBJECT_MAPPER))
            .build();
    }

    @Test
    void getAccessibleDbType() throws Exception {
        String actualResponseStr = "{ \"code\": 200, \"success\": true, \"data\": [ { \"id\": 1, \"name\": \"Mysql\" }, { \"id\": 2, \"name\": \"Oracle\" }, { \"id\": 3, \"name\": \"Postgresql\" }, { \"id\": 4, \"name\": \"Hive\" }, { \"id\": 5, \"name\": \"ClickHouse\" }, { \"id\": 6, \"name\": \"ES\" }, { \"id\": 7, \"name\": \"æµ·è´\u009D\" }, { \"id\": 12, \"name\": \"Nebula\" } ] }";
        ObjectNode actualResponse = JsonUtils.toJsonObject(actualResponseStr);
        mockMvc.perform(get("/out/service/dbType"))
            .andExpect(status().isOk())
            .andExpect(content().string(JsonUtils.toJsonString(actualResponse)));
    }

    @Test
    void getDbInfoByType() throws Exception {
        List<DbInfoResponse> dbInfoResponses = List.of(new DbInfoResponse(1, "test_name", 1, "mysql", "127.0.0.1", 3306, "root", "root", "test_db", "http"));
        StorageInfoListResponse storageInfoListResponse = new StorageInfoListResponse();
        storageInfoListResponse.setDbType(1);
        storageInfoListResponse.setDbInfos(dbInfoResponses);
        List<StorageInfoListResponse> response = List.of(storageInfoListResponse);
        ResponseMessage expectedResponse = ResponseMessage.ok(response);

        when(outService.getDbInfoByType(List.of(1))).thenReturn(response);
        mockMvc.perform(post("/out/service/dbInfo")
                .contentType("application/json")
                .content("[1]"))
            .andExpect(status().isOk())
            .andExpect(content().string(JsonUtils.toJsonString(expectedResponse)));
    }

    /**
     * 测试正常解密情况
     */
    @Test
    void testDecrypt_Success() {
        String encryptedText = "encryptedData";
        String decryptedText = "decryptedData";

        try (MockedStatic<AesUtils> aesUtilsMockedStatic = mockStatic(AesUtils.class)) {
            aesUtilsMockedStatic.when(() -> AesUtils.decrypt(encryptedText, AesUtils.DEFAULT_KEY))
                .thenReturn(decryptedText);

            String result = outApiController.decrypt(new PasswordRequest(encryptedText));

            assertEquals(decryptedText, result);
        }
    }

    /**
     * 测试解密失败的情况
     */
    @Test
    void testDecrypt_DecryptionFails_ShouldReturnError() throws Exception {
        String encryptedText = "invalidEncryptedData";

        try (MockedStatic<AesUtils> aesUtilsMockedStatic = mockStatic(AesUtils.class)) {
            aesUtilsMockedStatic.when(() -> AesUtils.decrypt(encryptedText, AesUtils.DEFAULT_KEY))
                .thenThrow(new RuntimeException("Decryption failed"));

            mockMvc.perform(post("/out/service/decrypt")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content("{\"encryptedPassword\":\"" + encryptedText + "\"}"))
                .andExpect(status().isInternalServerError()) // 500 Internal Server Error
                .andExpect(result -> assertInstanceOf(RuntimeException.class, result.getResolvedException()));
        }
    }

    /**
     * 测试空输入情况（验证参数校验）
     */
    @Test
    void testDecrypt_EmptyInput() throws Exception {
        mockMvc.perform(post("/out/service/decrypt")
                .contentType(MediaType.APPLICATION_JSON)
                .content("\"\"")) // 空字符串
            .andExpect(status().is5xxServerError()) // 应返回 500
            .andExpect(result -> assertInstanceOf(MethodArgumentNotValidException.class, result.getResolvedException()));
    }

    @Test
    void getTableStructs() throws Exception {
        TableStructDto tableStructDto = new TableStructDto();
        tableStructDto.setEnName("test");
        tableStructDto.setZhName("test_zhName");
        tableStructDto.setDataModelId(1);
        TableField tableField = new TableField();
        tableField.setEnName("id");
        tableField.setZhName("primary key");
        tableField.setType("int");
        tableField.setTypeName("int");
        tableField.setDataModelId(1);
        tableField.setId(1);
        tableStructDto.setFields(List.of(tableField));

        ResponseMessage expectedResponse = ResponseMessage.ok(List.of(tableStructDto));
        when(outService.getTableStructInfos(1)).thenReturn(List.of(tableStructDto));
        mockMvc.perform(get("/out/service/getTableStruct/1")
                .contentType(MediaType.APPLICATION_JSON)
                // 防止中文乱码
                .characterEncoding("UTF-8"))
            .andExpect(status().isOk())
            .andExpect(content().string(JsonUtils.toJsonString(expectedResponse)));
    }

    @Test
    void doExecuteSQL() throws Exception {

        Map<String, Object> item = JsonUtils.parseObject(
            "{\"tag\": [ { \"id\": \"3\", \"moyeTagType\": \"test_create_tag\", \"vid\": \"vid-ly\", \"name\": \"来一\", \"age\": \"2\" }, { \"age\": \"27\", \"moyeTagType\": \"test_create_tag\", \"vid\": \"vid-tch\", \"id\": \"4\", \"name\": \"谭朝宏\" }, { \"id\": \"2\", \"moyeTagType\": \"test_create_tag\", \"vid\": \"vid-zhb\", \"name\": \"张晗冰\", \"age\": \"25\" } ], \"edge\": [ { \"moyeEdgeType\": \"test_create_edge\", \"from\": \"vid-ly\", \"to\": \"vid-tch\", \"edge_name\": \"来一和谭朝宏是伙伴\", \"relation\": \"来一和谭朝宏是好伙伴\" }, { \"moyeEdgeType\": \"test_create_edge\", \"from\": \"vid-zhb\", \"to\": \"vid-ly\", \"edge_name\": \"张晗冰和来一是伙伴\", \"relation\": \"张晗冰和来一是好伙伴\" }, { \"moyeEdgeType\": \"test_create_edge\", \"from\": \"vid-tch\", \"to\": \"vid-zhb\", \"edge_name\": \"谭朝宏和张晗冰是伙伴\", \"relation\": \"谭朝宏和张晗冰是好伙伴\" } ]}"
            , new TypeReference<>() {
            });
        NativeStatementExecuteResult<Map<String, Object>> response = new NativeStatementExecuteResult<>();
        response.setItems(List.of(item));
        ResponseMessage expectedResponse = ResponseMessage.ok(response);

        String requestStr = "{ \"dbId\": 3, \"sql\": \"MATCH (v:test_create_tag)-[e:test_create_edge]->(v2:test_create_tag) RETURN v, e, v2;\", \"pageInfo\": { \"pageIndex\": \"1\", \"pageSize\": \"10\" } }";
        SqlRequest sqlRequest = JsonUtils.parseObject(requestStr, SqlRequest.class);
        assert sqlRequest != null;
        when(outService.executeSQL(sqlRequest)).thenReturn(response);

        mockMvc.perform(post("/out/service/execute/sql")
                .contentType(MediaType.APPLICATION_JSON)
                .accept("application/json;charset=UTF-8")
                .content(requestStr))
            .andExpect(status().isOk())
            .andExpect(content().string(JsonUtils.toJsonString(expectedResponse)));
    }
}